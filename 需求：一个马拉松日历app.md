# 需求：一个马拉松日历app

## 实现功能

### app前端

1. 使用flutter实现  
2. 页面整体布局为底部3个TAB
3. 第一个TAB名字为比赛
4. 第二个为日历
5. 第三个为我的
6. 比赛页面从后端获取当前比赛列表，一页10条数据，分页拉取，支持下拉刷新
7. 每一条比赛数据点击后进入比赛详情
8. 比赛详情页面使用inappwebview来实现，顶部可以点击收藏和分享
9. 比赛页面顶部有搜索框，点击进入搜索页面
10. 搜索页面列出对应搜索词对应的比赛列表
11. 日历页面上方展示当月日历，左右滑动可切换月份，下方展示具体某一天的比赛列表
12. 我的页面展示登录按钮（如果未登录），或头像+昵称，下面展示关于、隐私协议。


### app后端

1. 使用python+flask+mongodb实现
2. 提供一个接口，返回当前比赛列表
3. 提供一个接口，返回具体某一天的比赛列表
4. 提供一个接口，返回搜索词对应的比赛列表
5. 提供一个接口，返回比赛详情
6. 提供一个接口，返回用户信息
7. 提供一个接口，返回用户收藏的比赛列表

### 爬虫服务

1. 编写一个爬虫服务，每天2点/6点/10点/14点/18点/22点爬取数据，并存到数据库中